<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>RK Player</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <property name="spacing">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <spacer name="verticalSpacer">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>20</width>
        <height>545</height>
       </size>
      </property>
     </spacer>
    </item>
    <item row="1" column="0">
     <widget class="QWidget" name="videoWidget" native="true">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QSlider" name="progressSlider">
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="minimum">
          <number>0</number>
         </property>
         <property name="maximum">
          <number>100</number>
         </property>
         <property name="value">
          <number>0</number>
         </property>
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QPushButton" name="rewindButton">
           <property name="minimumSize">
            <size>
             <width>40</width>
             <height>35</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>40</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>14</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{  
border-radius:5px;
background-position: top;
      background-repeat: repeat-no-repeat;        
	
	image: url(:/icons/快退.png);
	color: rgb(255, 255, 255);
text-align : bottom;
 padding:0px;

}
</string>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="playPauseButton">
           <property name="minimumSize">
            <size>
             <width>45</width>
             <height>35</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>45</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>16</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{  
border-radius:5px;
      background-repeat: repeat-no-repeat;        
	image: url(:/icons/暂停.png);

	color: rgb(255, 255, 255);
 padding:0px;

}
QPushButton::pressed,QPushButton::checked{
	color: rgb(0, 0, 0);
	image: url(:/icons/播放.png);
}
</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
           <property name="checked">
            <bool>false</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="fastForwardButton">
           <property name="minimumSize">
            <size>
             <width>40</width>
             <height>35</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>40</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>14</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{  
border-radius:5px;
      background-repeat: repeat-no-repeat;        
	image: url(:/icons/快进.png);

	color: rgb(255, 255, 255);
 padding:0px;

}</string>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="timeLabel">
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>0</height>
            </size>
           </property>
           <property name="text">
            <string>00:00/00:00</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="exit_Button">
           <property name="minimumSize">
            <size>
             <width>40</width>
             <height>35</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>40</width>
             <height>35</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>14</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{  
border-radius:5px;
      background-repeat: repeat-no-repeat;        
	image: url(:/icons/退出.png);

	color: rgb(255, 255, 255);
 padding:0px;

}</string>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
