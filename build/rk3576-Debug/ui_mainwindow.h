/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.15.11
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSlider>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QGridLayout *gridLayout;
    QSpacerItem *verticalSpacer;
    QWidget *videoWidget;
    QVBoxLayout *verticalLayout;
    QSlider *progressSlider;
    QHBoxLayout *horizontalLayout;
    QPushButton *rewindButton;
    QPushButton *playPauseButton;
    QPushButton *fastForwardButton;
    QLabel *timeLabel;
    QSpacerItem *horizontalSpacer;
    QPushButton *exit_Button;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(800, 600);
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        gridLayout = new QGridLayout(centralwidget);
        gridLayout->setSpacing(0);
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        gridLayout->setContentsMargins(0, 0, 0, 0);
        verticalSpacer = new QSpacerItem(20, 545, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout->addItem(verticalSpacer, 0, 0, 1, 1);

        videoWidget = new QWidget(centralwidget);
        videoWidget->setObjectName(QString::fromUtf8("videoWidget"));
        videoWidget->setMinimumSize(QSize(0, 0));
        videoWidget->setStyleSheet(QString::fromUtf8(""));
        verticalLayout = new QVBoxLayout(videoWidget);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        progressSlider = new QSlider(videoWidget);
        progressSlider->setObjectName(QString::fromUtf8("progressSlider"));
        progressSlider->setStyleSheet(QString::fromUtf8(""));
        progressSlider->setMinimum(0);
        progressSlider->setMaximum(100);
        progressSlider->setValue(0);
        progressSlider->setOrientation(Qt::Horizontal);

        verticalLayout->addWidget(progressSlider);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        rewindButton = new QPushButton(videoWidget);
        rewindButton->setObjectName(QString::fromUtf8("rewindButton"));
        rewindButton->setMinimumSize(QSize(40, 35));
        rewindButton->setMaximumSize(QSize(40, 35));
        QFont font;
        font.setPointSize(14);
        rewindButton->setFont(font);
        rewindButton->setStyleSheet(QString::fromUtf8("QPushButton{  \n"
"border-radius:5px;\n"
"background-position: top;\n"
"      background-repeat: repeat-no-repeat;        \n"
"	\n"
"	image: url(:/icons/\345\277\253\351\200\200.png);\n"
"	color: rgb(255, 255, 255);\n"
"text-align : bottom;\n"
" padding:0px;\n"
"\n"
"}\n"
""));

        horizontalLayout->addWidget(rewindButton);

        playPauseButton = new QPushButton(videoWidget);
        playPauseButton->setObjectName(QString::fromUtf8("playPauseButton"));
        playPauseButton->setMinimumSize(QSize(45, 35));
        playPauseButton->setMaximumSize(QSize(45, 35));
        QFont font1;
        font1.setPointSize(16);
        playPauseButton->setFont(font1);
        playPauseButton->setStyleSheet(QString::fromUtf8("QPushButton{  \n"
"border-radius:5px;\n"
"      background-repeat: repeat-no-repeat;        \n"
"	image: url(:/icons/\346\232\202\345\201\234.png);\n"
"\n"
"	color: rgb(255, 255, 255);\n"
" padding:0px;\n"
"\n"
"}\n"
"QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	image: url(:/icons/\346\222\255\346\224\276.png);\n"
"}\n"
""));
        playPauseButton->setCheckable(true);
        playPauseButton->setChecked(false);

        horizontalLayout->addWidget(playPauseButton);

        fastForwardButton = new QPushButton(videoWidget);
        fastForwardButton->setObjectName(QString::fromUtf8("fastForwardButton"));
        fastForwardButton->setMinimumSize(QSize(40, 35));
        fastForwardButton->setMaximumSize(QSize(40, 35));
        fastForwardButton->setFont(font);
        fastForwardButton->setStyleSheet(QString::fromUtf8("QPushButton{  \n"
"border-radius:5px;\n"
"      background-repeat: repeat-no-repeat;        \n"
"	image: url(:/icons/\345\277\253\350\277\233.png);\n"
"\n"
"	color: rgb(255, 255, 255);\n"
" padding:0px;\n"
"\n"
"}"));

        horizontalLayout->addWidget(fastForwardButton);

        timeLabel = new QLabel(videoWidget);
        timeLabel->setObjectName(QString::fromUtf8("timeLabel"));
        timeLabel->setMinimumSize(QSize(100, 0));

        horizontalLayout->addWidget(timeLabel);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer);

        exit_Button = new QPushButton(videoWidget);
        exit_Button->setObjectName(QString::fromUtf8("exit_Button"));
        exit_Button->setMinimumSize(QSize(40, 35));
        exit_Button->setMaximumSize(QSize(40, 35));
        exit_Button->setFont(font);
        exit_Button->setStyleSheet(QString::fromUtf8("QPushButton{  \n"
"border-radius:5px;\n"
"      background-repeat: repeat-no-repeat;        \n"
"	image: url(:/icons/\351\200\200\345\207\272.png);\n"
"\n"
"	color: rgb(255, 255, 255);\n"
" padding:0px;\n"
"\n"
"}"));

        horizontalLayout->addWidget(exit_Button);


        verticalLayout->addLayout(horizontalLayout);


        gridLayout->addWidget(videoWidget, 1, 0, 1, 1);

        MainWindow->setCentralWidget(centralwidget);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "RK Player", nullptr));
        rewindButton->setText(QString());
        playPauseButton->setText(QString());
        fastForwardButton->setText(QString());
        timeLabel->setText(QCoreApplication::translate("MainWindow", "00:00/00:00", nullptr));
        exit_Button->setText(QString());
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
